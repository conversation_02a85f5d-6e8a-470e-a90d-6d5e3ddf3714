import React, { useEffect } from 'react';
import { useLenisSmoothScroll } from './hooks/useLenis';
import { initScrollReveal } from './animations/scrollAnimations';
import Header from './components/Header';
import Hero from './components/Hero';
import About from './components/About';
import ProductShowcase from './components/ProductShowcase';
import FeaturedCollection from './components/FeaturedCollection';
import Testimonials from './components/Testimonials';
import Newsletter from './components/Newsletter';
import ChocolateShowcase from './components/ChocolateShowcase';
import Footer from './components/Footer';

import ScrollProgress from './components/ScrollProgress';

function App() {
  // Initialize Lenis once
  useLenisSmoothScroll();

  // Initialise GSAP scroll-based reveal once after mount
  useEffect(() => {
    initScrollReveal();
  }, []);

  return (
    <div className="min-h-screen">
      <ScrollProgress />
      <Header />
      <Hero />
      <About />
      <ProductShowcase />
      <FeaturedCollection />
      <Testimonials />
      <Newsletter />
      <ChocolateShowcase />
      <Footer />
    </div>
  );
}

export default App;
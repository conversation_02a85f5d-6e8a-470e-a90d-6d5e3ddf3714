import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

/**
 * Attach GSAP-powered reveal animations to elements that have the data-animate attribute.
 * The attribute value decides the animation direction: up | down | left | right | zoom.
 */
export const initScrollReveal = () => {
  const elements = document.querySelectorAll<HTMLElement>('[data-animate]');

  elements.forEach((el) => {
    const direction = el.dataset.animate || 'up';

    let vars: gsap.TweenVars = { opacity: 0, ease: 'power2.out' };

    switch (direction) {
      case 'down':
        vars.y = -50;
        break;
      case 'left':
        vars.x = 50;
        break;
      case 'right':
        vars.x = -50;
        break;
      case 'zoom':
        vars.scale = 0.8;
        break;
      default:
        vars.y = 50; // up
    }

    gsap.from(el, {
      ...vars,
      duration: 1,
      scrollTrigger: {
        trigger: el,
        start: 'top 90%',
      },
    });
  });
};

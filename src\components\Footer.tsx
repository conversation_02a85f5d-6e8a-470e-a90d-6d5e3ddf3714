import React from 'react';
import { MapPin, Phone, Mail, Instagram, Facebook, Twitter, Clock, CreditCard } from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-amber-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16 grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-6">
            <div>
              <h3 className="text-2xl font-serif mb-2">
                <span className="text-white">Artisan</span>
                <span className="text-amber-300">Chocolat</span>
              </h3>
              <p className="text-amber-100 leading-relaxed">
                Crafting exceptional chocolate experiences since 1952. 
                From bean to bar, every piece tells a story of passion and perfection.
              </p>
            </div>
            
            {/* Social Links */}
            <div className="flex space-x-4">
              <a href="#" className="bg-amber-800 p-3 rounded-full hover:bg-amber-700 transition-colors duration-300">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="bg-amber-800 p-3 rounded-full hover:bg-amber-700 transition-colors duration-300">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="bg-amber-800 p-3 rounded-full hover:bg-amber-700 transition-colors duration-300">
                <Twitter className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-amber-300">Quick Links</h4>
            <ul className="space-y-3">
              {['About Us', 'Our Products', 'Gift Cards', 'Wholesale', 'Careers', 'Press Kit'].map((link) => (
                <li key={link}>
                  <a href="#" className="text-amber-100 hover:text-white transition-colors duration-300">
                    {link}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Customer Service */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-amber-300">Customer Service</h4>
            <ul className="space-y-3">
              {['Contact Us', 'Shipping Info', 'Returns & Exchanges', 'Size Guide', 'FAQ', 'Track Your Order'].map((link) => (
                <li key={link}>
                  <a href="#" className="text-amber-100 hover:text-white transition-colors duration-300">
                    {link}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-6 text-amber-300">Visit Our Store</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-amber-300 mt-0.5 flex-shrink-0" />
                <div className="text-amber-100">
                  <p>123 Chocolate Avenue</p>
                  <p>Sweet City, SC 12345</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-amber-300 flex-shrink-0" />
                <a href="tel:+1234567890" className="text-amber-100 hover:text-white transition-colors">
                  (*************
                </a>
              </div>
              
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-amber-300 flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-amber-100 hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </div>

              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-amber-300 mt-0.5 flex-shrink-0" />
                <div className="text-amber-100 text-sm">
                  <p>Mon-Fri: 9am-8pm</p>
                  <p>Sat-Sun: 10am-6pm</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-amber-800"></div>

        {/* Bottom Footer */}
        <div className="py-8 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="text-amber-100 text-sm">
            <p>&copy; {currentYear} Artisan Chocolat. All rights reserved.</p>
          </div>

          {/* Payment Methods */}
          <div className="flex items-center space-x-4">
            <span className="text-amber-300 text-sm font-semibold">We Accept:</span>
            <div className="flex items-center space-x-2">
              <div className="bg-white rounded p-2">
                <CreditCard className="h-4 w-4 text-gray-600" />
              </div>
              <div className="bg-white rounded p-2">
                <CreditCard className="h-4 w-4 text-gray-600" />
              </div>
              <div className="bg-white rounded p-2">
                <CreditCard className="h-4 w-4 text-gray-600" />
              </div>
            </div>
          </div>

          {/* Legal Links */}
          <div className="flex space-x-6 text-amber-100 text-sm">
            <a href="#" className="hover:text-white transition-colors duration-300">Privacy Policy</a>
            <a href="#" className="hover:text-white transition-colors duration-300">Terms of Service</a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
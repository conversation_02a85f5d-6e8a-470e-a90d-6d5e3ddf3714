import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

/**
 * Injects a Three.js canvas into the #loading element and spins the chocolate model.
 * The user must place a glTF/GLB file at public/assets/chocolate.glb.
 */
export const startChocolateScene = () => {
  const wrapper = document.getElementById('loading');
  if (!wrapper) return;

  const canvas = document.createElement('canvas');
  wrapper.appendChild(canvas);

  const renderer = new THREE.WebGLRenderer({ canvas, alpha: true, antialias: true });
  renderer.setPixelRatio(window.devicePixelRatio);

  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(35, 1, 0.1, 100);
  camera.position.set(0, 0, 6);

  // Soft ambient light + key light
  scene.add(new THREE.AmbientLight(0xffffff, 0.6));
  const dirLight = new THREE.DirectionalLight(0xffffff, 0.8);
  dirLight.position.set(5, 5, 5);
  scene.add(dirLight);

  // Resize handling
  const resize = () => {
    const size = Math.min(wrapper.clientWidth, wrapper.clientHeight) * 0.6;
    renderer.setSize(size, size);
    camera.aspect = 1;
    camera.updateProjectionMatrix();
  };
  resize();
  window.addEventListener('resize', resize);

  // Load model
  const loader = new GLTFLoader();
  loader.load('/assets/chocolate.glb', (gltf) => {
    const model = gltf.scene;
    scene.add(model);

    // Center model
    const box = new THREE.Box3().setFromObject(model);
    const center = new THREE.Vector3();
    box.getCenter(center);
    model.position.sub(center);

    // Animation loop
    const tick = () => {
      model.rotation.y += 0.01;
      renderer.render(scene, camera);
      requestAnimationFrame(tick);
    };
    tick();
  });
};

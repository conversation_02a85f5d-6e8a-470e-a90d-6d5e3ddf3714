@tailwind base;
@tailwind components;
@tailwind utilities;

/* Sticky chocolate progress bar with dripping effect */
.progress-bar-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 20px;
  z-index: 9999;
  background: transparent;
  pointer-events: none;
}

.progress-bar {
  height: 8px;
  width: var(--scroll-progress, 0%);
  position: relative;
  background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #A0522D 100%);
  transition: width 0.2s ease-out;
  box-shadow: 0 2px 4px rgba(139, 69, 19, 0.3);
  /* Create wavy dripping bottom edge using clip-path */
  clip-path: polygon(
    0% 0%, 100% 0%, 100% 60%,
    97% 95%, 94% 70%, 91% 100%, 87% 75%, 84% 90%, 
    80% 100%, 76% 65%, 72% 85%, 68% 100%, 64% 70%, 
    60% 95%, 56% 100%, 52% 80%, 48% 90%, 44% 100%, 
    40% 75%, 36% 85%, 32% 100%, 28% 70%, 24% 90%, 
    20% 100%, 16% 80%, 12% 95%, 8% 100%, 4% 85%, 0% 75%
  );
}

/* Add glossy highlight */
.progress-bar::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.4) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    transparent 100%);
  clip-path: polygon(
    0% 0%, 100% 0%, 100% 100%,
    97% 80%, 94% 50%, 91% 85%, 87% 45%, 84% 70%, 
    80% 90%, 76% 40%, 72% 65%, 68% 85%, 64% 50%, 
    60% 75%, 56% 90%, 52% 60%, 48% 70%, 44% 85%, 
    40% 55%, 36% 65%, 32% 80%, 28% 50%, 24% 70%, 
    20% 85%, 16% 60%, 12% 75%, 8% 80%, 4% 65%, 0% 55%
  );
}


/* Custom animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

/* Lenis handles smooth scrolling, so disable native behaviour */
html {
  scroll-behavior: auto;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #d97706;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b45309;
}

/* Loading animation for newsletter */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Better text rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Focus states for accessibility */
button:focus,
input:focus,
a:focus {
  outline: 2px solid #d97706;
  outline-offset: 2px;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom box shadows */
.shadow-chocolate {
  box-shadow: 0 10px 25px -5px rgba(217, 119, 6, 0.1), 0 10px 10px -5px rgba(217, 119, 6, 0.04);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

/* Background patterns */
.chocolate-pattern {
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(217, 119, 6, 0.15) 1px, transparent 0);
  background-size: 20px 20px;
}
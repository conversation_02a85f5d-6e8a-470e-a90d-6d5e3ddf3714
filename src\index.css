@tailwind base;
@tailwind components;
@tailwind utilities;

/* Sticky chocolate progress bar with dripping effect using PNG */
.progress-bar-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  z-index: 9999;
  background: transparent;
  pointer-events: none;
  overflow: hidden;
}

.progress-bar {
  position: relative;
  width: var(--scroll-progress, 0%);
  height: 100%;
  transition: width 0.2s ease-out;
}

.progress-drip-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
  object-fit: cover;
  object-position: left top;
  pointer-events: none;
}

/* Custom animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

/* <PERSON><PERSON> handles smooth scrolling, so disable native behaviour */
html {
  scroll-behavior: auto;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #d97706;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b45309;
}

/* Loading animation for newsletter */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Better text rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Focus states for accessibility */
button:focus,
input:focus,
a:focus {
  outline: 2px solid #d97706;
  outline-offset: 2px;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom box shadows */
.shadow-chocolate {
  box-shadow: 0 10px 25px -5px rgba(217, 119, 6, 0.1),
    0 10px 10px -5px rgba(217, 119, 6, 0.04);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

/* Background patterns */
.chocolate-pattern {
  background-image: radial-gradient(
    circle at 1px 1px,
    rgba(217, 119, 6, 0.15) 1px,
    transparent 0
  );
  background-size: 20px 20px;
}

import { useEffect, useState } from "react";

const ScrollProgress: React.FC = () => {
  const [dripElements, setDripElements] = useState<
    Array<{ id: number; image: string; position: number; mirrored: boolean }>
  >([]);

  useEffect(() => {
    // Generate alternating normal and mirrored drip elements
    const generateDrips = () => {
      const drips = [];
      const dripCount = 50; // More drip elements for better coverage
      const dripWidth = 40; // Smaller spacing for continuous coverage

      for (let i = 0; i < dripCount; i++) {
        drips.push({
          id: i,
          image: "/assets/drip.png",
          position: i * dripWidth,
          mirrored: i % 2 === 1, // Alternate: normal (even), mirrored (odd)
        });
      }
      setDripElements(drips);
    };

    generateDrips();

    const updateProgress = () => {
      const scrolled = window.scrollY;
      const height = document.documentElement.scrollHeight - window.innerHeight;
      const progress = height > 0 ? (scrolled / height) * 100 : 0;
      document.documentElement.style.setProperty(
        "--scroll-progress",
        `${progress}%`
      );
    };
    updateProgress();
    window.addEventListener("scroll", updateProgress, { passive: true });
    return () => window.removeEventListener("scroll", updateProgress);
  }, []);

  return (
    <div className="progress-bar-wrapper pointer-events-none">
      <div className="progress-bar">
        {dripElements.map((drip) => (
          <img
            key={drip.id}
            src={drip.image}
            alt="Chocolate drip"
            className="progress-drip-element"
            style={{
              left: `${drip.position}px`,
              transform: drip.mirrored ? "scaleX(-1)" : "none",
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default ScrollProgress;

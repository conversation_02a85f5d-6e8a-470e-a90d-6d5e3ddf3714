import { useEffect, useState } from "react";

const ScrollProgress: React.FC = () => {
  const [dripElements, setDripElements] = useState<
    Array<{ id: number; image: string; position: number }>
  >([]);

  useEffect(() => {
    // Generate random drip elements
    const generateDrips = () => {
      const drips = [];
      const dripCount = 50; // More drip elements for better coverage
      const dripWidth = 40; // Smaller spacing for continuous coverage

      for (let i = 0; i < dripCount; i++) {
        drips.push({
          id: i,
          image: Math.random() > 0.5 ? "/assets/drip.png" : "/assets/drip2.png",
          position: i * dripWidth,
        });
      }
      setDripElements(drips);
    };

    generateDrips();

    const updateProgress = () => {
      const scrolled = window.scrollY;
      const height = document.documentElement.scrollHeight - window.innerHeight;
      const progress = height > 0 ? (scrolled / height) * 100 : 0;
      document.documentElement.style.setProperty(
        "--scroll-progress",
        `${progress}%`
      );
    };
    updateProgress();
    window.addEventListener("scroll", updateProgress, { passive: true });
    return () => window.removeEventListener("scroll", updateProgress);
  }, []);

  return (
    <div className="progress-bar-wrapper pointer-events-none">
      <div className="progress-bar">
        {dripElements.map((drip) => (
          <img
            key={drip.id}
            src={drip.image}
            alt="Chocolate drip"
            className="progress-drip-element"
            style={{ left: `${drip.position}px` }}
          />
        ))}
      </div>
    </div>
  );
};

export default ScrollProgress;

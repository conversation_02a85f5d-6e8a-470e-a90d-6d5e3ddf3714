import { useEffect } from 'react';

const ScrollProgress: React.FC = () => {
  useEffect(() => {
    const updateProgress = () => {
      const scrolled = window.scrollY;
      const height = document.documentElement.scrollHeight - window.innerHeight;
      const progress = height > 0 ? (scrolled / height) * 100 : 0;
      document.documentElement.style.setProperty('--scroll-progress', `${progress}%`);
    };
    updateProgress();
    window.addEventListener('scroll', updateProgress, { passive: true });
    return () => window.removeEventListener('scroll', updateProgress);
  }, []);

  return (
    <div className="progress-bar-wrapper pointer-events-none">
      <div className="progress-bar">
        <img
          src="/assets/drip.png"
          alt="Chocolate drip progress"
          className="progress-drip-image"
        />
      </div>
    </div>
  );
};

export default ScrollProgress;

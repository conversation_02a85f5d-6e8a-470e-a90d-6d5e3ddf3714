/**
 * Creates and displays a static drip PNG image that fills the loading overlay.
 * The overlay must have the id `loading` (as present in index.html).
 */
export const startChocolateDrip = () => {
  const loading = document.getElementById("loading");
  if (!loading) return;

  // Create container for the drip image
  const container = document.createElement("div");
  container.className = "drip-container";
  loading.appendChild(container);

  // Create the drip image element
  const dripImage = document.createElement("img");
  dripImage.src = "/assets/drip.png";
  dripImage.alt = "Chocolate drip";
  dripImage.className = "drip-image";

  // Style the image to fill the container
  dripImage.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    pointer-events: none;
    z-index: 1;
  `;

  container.appendChild(dripImage);

  // Remove container when loading overlay is taken out of the DOM
  const observer = new MutationObserver(() => {
    if (!document.body.contains(loading)) {
      observer.disconnect();
      container.remove();
    }
  });
  observer.observe(document.body, { childList: true, subtree: true });
};

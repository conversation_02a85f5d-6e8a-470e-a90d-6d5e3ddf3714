/**
 * Creates a slick CSS-only loading animation without external resources.
 * The overlay must have the id `loading` (as present in index.html).
 */
export const startSlickLoader = () => {
  const loading = document.getElementById("loading");
  if (!loading) return;

  // Create the brand text
  const brandText = document.createElement("div");
  brandText.className = "loading-brand-text";
  brandText.innerHTML = `
    <span class="brand-artisan">Artisan</span>
    <span class="brand-chocolat">Chocolat</span>
  `;

  // Create animated chocolate bars
  const barsContainer = document.createElement("div");
  barsContainer.className = "chocolate-bars-container";

  for (let i = 0; i < 5; i++) {
    const bar = document.createElement("div");
    bar.className = "chocolate-bar";
    bar.style.animationDelay = `${i * 0.2}s`;
    barsContainer.appendChild(bar);
  }

  // Create pulsing dots
  const dotsContainer = document.createElement("div");
  dotsContainer.className = "loading-dots";

  for (let i = 0; i < 3; i++) {
    const dot = document.createElement("div");
    dot.className = "loading-dot";
    dot.style.animationDelay = `${i * 0.3}s`;
    dotsContainer.appendChild(dot);
  }

  // Add all elements to loading container
  loading.appendChild(brandText);
  loading.appendChild(barsContainer);
  loading.appendChild(dotsContainer);

  // Remove elements when loading overlay is taken out of the DOM
  const observer = new MutationObserver(() => {
    if (!document.body.contains(loading)) {
      observer.disconnect();
      brandText.remove();
      barsContainer.remove();
      dotsContainer.remove();
    }
  });
  observer.observe(document.body, { childList: true, subtree: true });
};

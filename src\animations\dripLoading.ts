import anime from 'animejs';

/**
 * Creates and starts an infinite chocolate–dripping animation on the loading overlay.
 * The overlay must have the id `loading` (as present in index.html).
 */
export const startChocolateDrip = () => {
  const loading = document.getElementById('loading');
  if (!loading) return;

  // Create container for droplets so we can easily clean up later
  const container = document.createElement('div');
  container.className = 'drip-container';
  loading.appendChild(container);

    // Create droplets with varied sizes
  const DROPS = 22;
  for (let i = 0; i < DROPS; i++) {
    const drop = document.createElement('span');
    drop.className = 'choc-drop';

    // Random width/height for more organic look
    const w = 12 + Math.random() * 18; // 12–30px
    const h = w * 1.4; // maintain oval shape
    drop.style.width = `${w}px`;
    drop.style.height = `${h}px`;

    // Random starting X position
    drop.style.left = `${Math.random() * 100}%`;

    container.appendChild(drop);
  }

  // Advanced Anime.js timeline – fall then splat
  anime.timeline({ loop: true })
    .add({
      targets: container.children,
      translateY: [ -60, window.innerHeight * 0.55 ],
      scaleY: [1, 0.9],
      scaleX: [1, 0.9],
      opacity: [1, 1],
      easing: 'easeInQuad',
      duration: 1000,
      delay: anime.stagger(140),
    })
    .add({
      targets: container.children,
      scaleX: 1.4,
      scaleY: 0.3,
      duration: 220,
      easing: 'easeOutExpo',
      offset: '-=800', // overlap for smoother splat
    })
    .add({
      targets: container.children,
      opacity: 0,
      duration: 380,
      easing: 'linear',
      offset: '-=150',
    });

  // Remove container when loading overlay is taken out of the DOM
  const observer = new MutationObserver(() => {
    if (!document.body.contains(loading)) {
      observer.disconnect();
      container.remove();
    }
  });
  observer.observe(document.body, { childList: true, subtree: true });
};

import { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

const ChocolateShowcase: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    container.appendChild(renderer.domElement);

    // Scene & Camera
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(40, 1, 0.1, 100);
    camera.position.set(0, 0, 6);

    // Lights
    scene.add(new THREE.AmbientLight(0xffffff, 0.6));
    const keyLight = new THREE.DirectionalLight(0xffffff, 0.9);
    keyLight.position.set(5, 5, 5);
    scene.add(keyLight);

    // Handle resize
    const resize = () => {
      const { clientWidth, clientHeight } = container;
      renderer.setSize(clientWidth, clientHeight);
      camera.aspect = clientWidth / clientHeight;
      camera.updateProjectionMatrix();
    };
    resize();
    window.addEventListener('resize', resize);

    // Load the model
    const loader = new GLTFLoader();
    let model: THREE.Object3D;
    loader.load('/assets/chocolate.glb', (gltf) => {
      model = gltf.scene;
      // Center
      const box = new THREE.Box3().setFromObject(model);
      const center = new THREE.Vector3();
      box.getCenter(center);
      model.position.sub(center);

      scene.add(model);
    });

    // Animation loop
    const animate = () => {
      requestAnimationFrame(animate);
      if (model) {
        model.rotation.y += 0.008;
      }
      renderer.render(scene, camera);
    };
    animate();

    return () => {
      window.removeEventListener('resize', resize);
      renderer.dispose();
    };
  }, []);

  return (
    <section className="py-24 bg-amber-50 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-4xl md:text-5xl font-serif text-center text-amber-900 mb-10">
          Crafted in 3D Glory
        </h2>
        <p className="text-lg text-gray-700 text-center max-w-2xl mx-auto mb-10">
          Rotate and admire our signature chocolate bar rendered in 3-D, representing the precision and
          passion poured into every creation.
        </p>
        <div
          ref={containerRef}
          className="w-full h-[400px] sm:h-[500px] mx-auto rounded-xl shadow-lg overflow-hidden bg-gradient-to-br from-amber-200/40 to-white"
        />
      </div>
    </section>
  );
};

export default ChocolateShowcase;

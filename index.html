<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Artisan Chocolat - Handcrafted Luxury Chocolate</title>
    <meta
      name="description"
      content="Discover the finest handcrafted chocolates from Artisan Chocolat. Premium quality, sustainable sourcing, and unforgettable flavors since 1952."
    />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta
      property="og:title"
      content="Artisan Chocolat - Handcrafted Luxury Chocolate"
    />
    <meta
      property="og:description"
      content="Discover the finest handcrafted chocolates with premium quality and unforgettable flavors."
    />
    <meta
      property="og:image"
      content="https://images.pexels.com/photos/918327/pexels-photo-918327.jpeg"
    />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta
      property="twitter:title"
      content="Artisan Chocolat - Handcrafted Luxury Chocolate"
    />
    <meta
      property="twitter:description"
      content="Discover the finest handcrafted chocolates with premium quality and unforgettable flavors."
    />
    <meta
      property="twitter:image"
      content="https://images.pexels.com/photos/918327/pexels-photo-918327.jpeg"
    />

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://images.pexels.com" />
    <link rel="preconnect" href="https://assets.mixkit.co" />

    <!-- Google Fonts - Optimized loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      /* Prevent flash of unstyled content */
      html {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      /* Loading animation */
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #3c2a22;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 1;
        transition: opacity 0.5s ease-out;
      }

      .loading.fade-out {
        opacity: 0;
        pointer-events: none;
      }

      .loading-text {
        color: #d4af37;
        font-size: 1.5rem;
        font-weight: 600;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }
    </style>
    <!-- Anime.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/animejs@3.2.2/lib/anime.min.js"></script>
    <style>
      /* Drip image styles */
      .drip-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
      }
      .loading-brand {
        font-family: "Playfair Display", serif;
        font-size: 2.5rem;
        color: #5b3a2b;
        animation: pulseText 1.8s ease-in-out infinite;
      }
      @keyframes pulseText {
        0%,
        100% {
          opacity: 1;
          transform: scale(1);
        }
        50% {
          opacity: 0.6;
          transform: scale(1.08);
        }
      }
      .drip-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        pointer-events: none;
        z-index: 1;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading" class="loading">
      <!-- <h1 class="loading-brand">Artisan&nbsp;Chocolat</h1> -->
    </div>

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- chocolate drip loader -->
    <script type="module" src="/src/loadingAnimation.ts"></script>

    <!-- Remove loading screen after page loads -->
    <script>
      window.addEventListener("load", function () {
        setTimeout(function () {
          const loading = document.getElementById("loading");
          if (loading) {
            loading.classList.add("fade-out");
            setTimeout(() => loading.remove(), 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>

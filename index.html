<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Artisan Chocolat - Handcrafted Luxury Chocolate</title>
    <meta
      name="description"
      content="Discover the finest handcrafted chocolates from Artisan Chocolat. Premium quality, sustainable sourcing, and unforgettable flavors since 1952."
    />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta
      property="og:title"
      content="Artisan Chocolat - Handcrafted Luxury Chocolate"
    />
    <meta
      property="og:description"
      content="Discover the finest handcrafted chocolates with premium quality and unforgettable flavors."
    />
    <meta
      property="og:image"
      content="https://images.pexels.com/photos/918327/pexels-photo-918327.jpeg"
    />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta
      property="twitter:title"
      content="Artisan Chocolat - Handcrafted Luxury Chocolate"
    />
    <meta
      property="twitter:description"
      content="Discover the finest handcrafted chocolates with premium quality and unforgettable flavors."
    />
    <meta
      property="twitter:image"
      content="https://images.pexels.com/photos/918327/pexels-photo-918327.jpeg"
    />

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://images.pexels.com" />
    <link rel="preconnect" href="https://assets.mixkit.co" />

    <!-- Google Fonts - Optimized loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      /* Prevent flash of unstyled content */
      html {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      /* Loading animation */
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #3c2a22;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 1;
        transition: opacity 0.5s ease-out;
      }

      .loading.fade-out {
        opacity: 0;
        pointer-events: none;
      }

      .loading-text {
        color: #d4af37;
        font-size: 1.5rem;
        font-weight: 600;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }
    </style>
    <!-- Anime.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/animejs@3.2.2/lib/anime.min.js"></script>
    <style>
      /* Slick loading animations */
      .loading-brand-text {
        font-family: "Playfair Display", serif;
        font-size: 3rem;
        text-align: center;
        margin-bottom: 2rem;
        animation: fadeInUp 1s ease-out;
      }

      .brand-artisan {
        color: #8b4513;
        font-weight: 400;
        margin-right: 0.5rem;
        animation: slideInLeft 1s ease-out;
      }

      .brand-chocolat {
        color: #d2691e;
        font-weight: 600;
        animation: slideInRight 1s ease-out 0.3s both;
      }

      .chocolate-bars-container {
        display: flex;
        justify-content: center;
        gap: 8px;
        margin: 2rem 0;
      }

      .chocolate-bar {
        width: 8px;
        height: 40px;
        background: linear-gradient(
          135deg,
          #8b4513 0%,
          #d2691e 50%,
          #a0522d 100%
        );
        border-radius: 4px;
        animation: chocolateWave 1.5s ease-in-out infinite;
        box-shadow: 0 2px 8px rgba(139, 69, 19, 0.3);
      }

      .loading-dots {
        display: flex;
        justify-content: center;
        gap: 12px;
        margin-top: 2rem;
      }

      .loading-dot {
        width: 12px;
        height: 12px;
        background: #d2691e;
        border-radius: 50%;
        animation: dotPulse 1.4s ease-in-out infinite;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideInLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes slideInRight {
        from {
          opacity: 0;
          transform: translateX(50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes chocolateWave {
        0%,
        100% {
          transform: scaleY(1);
          opacity: 0.7;
        }
        50% {
          transform: scaleY(1.5);
          opacity: 1;
        }
      }

      @keyframes dotPulse {
        0%,
        100% {
          transform: scale(1);
          opacity: 0.5;
        }
        50% {
          transform: scale(1.3);
          opacity: 1;
        }
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading" class="loading">
      <!-- <h1 class="loading-brand">Artisan&nbsp;Chocolat</h1> -->
    </div>

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- chocolate drip loader -->
    <script type="module" src="/src/loadingAnimation.ts"></script>

    <!-- Remove loading screen after page loads -->
    <script>
      window.addEventListener("load", function () {
        setTimeout(function () {
          const loading = document.getElementById("loading");
          if (loading) {
            loading.classList.add("fade-out");
            setTimeout(() => loading.remove(), 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>

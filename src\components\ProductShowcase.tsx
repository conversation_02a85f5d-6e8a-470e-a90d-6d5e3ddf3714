import React, { useState } from 'react';
import { <PERSON><PERSON>art, Heart, Star } from 'lucide-react';

const ProductShowcase: React.FC = () => {
  const [filter, setFilter] = useState('All');
  const [favorites, setFavorites] = useState<number[]>([]);

  const products = [
    {
      id: 1,
      name: "Madagascar Dark",
      category: "Dark",
      price: 24.99,
      rating: 4.9,
      image: "https://images.pexels.com/photos/918327/pexels-photo-918327.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
      description: "Rich 70% dark chocolate with notes of red fruit and vanilla",
      featured: true
    },
    {
      id: 2,
      name: "Himalayan Salt Caramel",
      category: "Milk",
      price: 26.99,
      rating: 4.8,
      image: "https://images.pexels.com/photos/3778775/pexels-photo-3778775.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
      description: "Creamy milk chocolate with house-made salted caramel center"
    },
    {
      id: 3,
      name: "White Raspberry",
      category: "White",
      price: 22.99,
      rating: 4.7,
      image: "https://images.pexels.com/photos/4040652/pexels-photo-4040652.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
      description: "Smooth white chocolate infused with freeze-dried raspberries"
    },
    {
      id: 4,
      name: "Espresso Crunch",
      category: "Dark",
      price: 25.99,
      rating: 4.9,
      image: "https://images.pexels.com/photos/6802983/pexels-photo-6802983.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
      description: "85% dark chocolate with roasted coffee beans and cacao nibs",
      featured: true
    },
    {
      id: 5,
      name: "Honey Lavender",
      category: "Milk",
      price: 23.99,
      rating: 4.6,
      image: "https://images.pexels.com/photos/3778775/pexels-photo-3778775.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
      description: "Delicate milk chocolate with organic honey and French lavender"
    },
    {
      id: 6,
      name: "Sea Salt & Olive Oil",
      category: "Dark",
      price: 27.99,
      rating: 4.8,
      image: "https://images.pexels.com/photos/918327/pexels-photo-918327.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&fit=crop",
      description: "Premium dark chocolate with Mediterranean sea salt and extra virgin olive oil"
    }
  ];

  const categories = ['All', 'Dark', 'Milk', 'White'];

  const filteredProducts = filter === 'All' 
    ? products 
    : products.filter(product => product.category === filter);

  const toggleFavorite = (productId: number) => {
    setFavorites(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  return (
    <section id="products" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif text-amber-900 mb-6">
            Our Chocolate Collection
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
            Each bar is a masterpiece, carefully crafted with the finest ingredients 
            and perfected through generations of chocolate-making expertise.
          </p>

          {/* Filter Buttons */}
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setFilter(category)}
                className={`px-6 py-2 rounded-full font-semibold transition-all duration-300 ${
                  filter === category
                    ? 'bg-amber-600 text-white shadow-lg'
                    : 'bg-amber-100 text-amber-800 hover:bg-amber-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Product Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              className="bg-white rounded-2xl shadow-lg overflow-hidden group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-amber-50"
            >
              {/* Product Image */}
              <div className="relative overflow-hidden">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                />
                {product.featured && (
                  <div className="absolute top-4 left-4 bg-amber-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Featured
                  </div>
                )}
                <button
                  onClick={() => toggleFavorite(product.id)}
                  className={`absolute top-4 right-4 p-2 rounded-full transition-all duration-300 ${
                    favorites.includes(product.id)
                      ? 'bg-red-500 text-white'
                      : 'bg-white/80 text-gray-600 hover:bg-white'
                  }`}
                >
                  <Heart className={`h-5 w-5 ${favorites.includes(product.id) ? 'fill-current' : ''}`} />
                </button>
              </div>

              {/* Product Info */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-amber-600 font-semibold uppercase tracking-wide">
                    {product.category}
                  </span>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-gray-600">{product.rating}</span>
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-amber-900 mb-2">{product.name}</h3>
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">{product.description}</p>
                
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-amber-900">${product.price}</span>
                  <button className="bg-amber-600 text-white px-4 py-2 rounded-full flex items-center space-x-2 hover:bg-amber-700 transition-colors duration-300 transform hover:scale-105">
                    <ShoppingCart className="h-4 w-4" />
                    <span className="font-semibold">Add to Cart</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductShowcase;
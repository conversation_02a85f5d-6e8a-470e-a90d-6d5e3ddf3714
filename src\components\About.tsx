import React from 'react';
import { Award, Heart, Leaf } from 'lucide-react';

const About: React.FC = () => {
  const features = [
    {
      icon: <Award className="h-8 w-8 text-amber-600" />,
      title: "Award Winning",
      description: "Recognized internationally for our exceptional quality and innovative flavors."
    },
    {
      icon: <Heart className="h-8 w-8 text-amber-600" />,
      title: "Handcrafted with Love",
      description: "Every piece is carefully crafted by our master chocolatiers with passion and precision."
    },
    {
      icon: <Leaf className="h-8 w-8 text-amber-600" />,
      title: "Sustainable Sourcing",
      description: "We partner directly with cocoa farmers to ensure fair trade and sustainable practices."
    }
  ];

  return (
    <section id="about" className="py-20 bg-amber-50/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div>
              <h2 className="text-4xl md:text-5xl font-serif text-amber-900 mb-6">
                Our Chocolate Story
              </h2>
              <p className="text-lg text-gray-700 leading-relaxed mb-6">
                For over three generations, our family has been perfecting the art of chocolate making. 
                What started as a small confectionery in 1952 has grown into a beloved artisan chocolate 
                brand, but our commitment to quality and tradition remains unchanged.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                We believe that great chocolate begins with great ingredients. That's why we source our 
                cocoa beans directly from sustainable farms in Ecuador, Madagascar, and Ghana, ensuring 
                every bar tells a story of its origin.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-amber-100">
                <h3 className="font-semibold text-amber-900 mb-2">Bean to Bar</h3>
                <p className="text-gray-600">Complete control over every step of the chocolate making process.</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-sm border border-amber-100">
                <h3 className="font-semibold text-amber-900 mb-2">Small Batches</h3>
                <p className="text-gray-600">Ensuring maximum freshness and quality in every creation.</p>
              </div>
            </div>
          </div>

          {/* Images */}
          <div className="relative">
            <div className="grid grid-cols-2 gap-4">
              <img
                src="https://images.pexels.com/photos/4040652/pexels-photo-4040652.jpeg?auto=compress&cs=tinysrgb&w=400&h=500&fit=crop"
                alt="Chocolatier crafting chocolate"
                className="w-full h-64 object-cover rounded-lg shadow-lg"
              />
              <img
                src="https://images.pexels.com/photos/3778775/pexels-photo-3778775.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
                alt="Cocoa beans"
                className="w-full h-48 object-cover rounded-lg shadow-lg mt-8"
              />
            </div>
            <img
              src="https://images.pexels.com/photos/6802983/pexels-photo-6802983.jpeg?auto=compress&cs=tinysrgb&w=500&h=300&fit=crop"
              alt="Chocolate workshop"
              className="w-full h-48 object-cover rounded-lg shadow-lg mt-4"
            />
          </div>
        </div>

        {/* Features */}
        <div className="mt-20 grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center group hover:transform hover:scale-105 transition-all duration-300">
              <div className="bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-amber-900 mb-2">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default About;
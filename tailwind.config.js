/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        amber: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        chocolate: {
          50: '#faf7f2',
          100: '#f4ebe0',
          200: '#e8d5c4',
          300: '#d4af37',
          400: '#c19a6b',
          500: '#8b4513',
          600: '#7a3f12',
          700: '#6b3410',
          800: '#5c2e0f',
          900: '#4e342e',
        }
      },
      fontFamily: {
        'serif': ['Playfair Display', 'Georgia', 'serif'],
        'sans': ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in-up': 'fadeInUp 0.8s ease-out forwards',
        'bounce': 'bounce 2s infinite',
        'pulse': 'pulse 2s infinite',
      },
      keyframes: {
        fadeInUp: {
          '0%': {
            opacity: '0',
            transform: 'translateY(30px)'
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)'
          }
        }
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      boxShadow: {
        'chocolate': '0 10px 25px -5px rgba(217, 119, 6, 0.1), 0 10px 10px -5px rgba(217, 119, 6, 0.04)',
        'chocolate-lg': '0 20px 40px -10px rgba(217, 119, 6, 0.15)',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'chocolate-pattern': 'radial-gradient(circle at 1px 1px, rgba(217, 119, 6, 0.15) 1px, transparent 0)',
      },
      backgroundSize: {
        'pattern': '20px 20px',
      }
    },
  },
  plugins: [],
};
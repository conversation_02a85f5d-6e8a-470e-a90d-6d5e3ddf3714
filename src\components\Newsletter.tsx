import React, { useState } from 'react';
import { Mail, Gift, CheckCircle } from 'lucide-react';

const Newsletter: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsSubscribed(true);
    setIsLoading(false);
    setEmail('');
  };

  if (isSubscribed) {
    return (
      <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12">
            <CheckCircle className="h-16 w-16 text-green-300 mx-auto mb-6" />
            <h3 className="text-3xl font-serif text-white mb-4">Welcome to the Family!</h3>
            <p className="text-xl text-amber-100 mb-6">
              Thank you for subscribing! Check your email for your exclusive 10% discount code.
            </p>
            <p className="text-amber-200">
              Get ready for delicious updates, exclusive offers, and chocolate inspiration!
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-r from-amber-600 to-amber-700">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12">
          {/* Icon */}
          <div className="flex items-center justify-center mb-6">
            <div className="bg-white/20 rounded-full p-4">
              <Gift className="h-8 w-8 text-white" />
            </div>
          </div>

          {/* Header */}
          <h3 className="text-3xl md:text-4xl font-serif text-white mb-4">
            Sweet Rewards Await
          </h3>
          <p className="text-xl text-amber-100 mb-8 leading-relaxed">
            Join our chocolate family and get <span className="font-bold text-white">10% off your first order</span>, 
            plus exclusive access to new flavors, special offers, and chocolate-making tips.
          </p>

          {/* Newsletter Form */}
          <form onSubmit={handleSubmit} className="max-w-md mx-auto">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className="w-full pl-12 pr-4 py-4 rounded-full border-0 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-white focus:outline-none"
                  required
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="bg-white text-amber-700 px-8 py-4 rounded-full font-semibold hover:bg-amber-50 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
              >
                {isLoading ? 'Subscribing...' : 'Get My Discount'}
              </button>
            </div>
          </form>

          {/* Benefits */}
          <div className="grid md:grid-cols-3 gap-6 mt-12 text-amber-100">
            <div className="flex flex-col items-center">
              <div className="bg-white/20 rounded-full p-3 mb-3">
                <Gift className="h-6 w-6 text-white" />
              </div>
              <h4 className="font-semibold text-white mb-1">Exclusive Offers</h4>
              <p className="text-sm">First access to sales and special promotions</p>
            </div>
            <div className="flex flex-col items-center">
              <div className="bg-white/20 rounded-full p-3 mb-3">
                <Mail className="h-6 w-6 text-white" />
              </div>
              <h4 className="font-semibold text-white mb-1">New Flavors</h4>
              <p className="text-sm">Be the first to try our latest creations</p>
            </div>
            <div className="flex flex-col items-center">
              <div className="bg-white/20 rounded-full p-3 mb-3">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <h4 className="font-semibold text-white mb-1">Chocolate Tips</h4>
              <p className="text-sm">Learn from our master chocolatiers</p>
            </div>
          </div>

          {/* Privacy Note */}
          <p className="text-sm text-amber-200 mt-8">
            We respect your privacy. Unsubscribe at any time. No spam, just sweet updates!
          </p>
        </div>
      </div>
    </section>
  );
};

export default Newsletter;
import React from 'react';
import { ArrowRight, Gift } from 'lucide-react';

const FeaturedCollection: React.FC = () => {
  const collections = [
    {
      id: 1,
      name: "Holiday Gift Box",
      description: "A curated selection of our finest chocolates, perfect for special occasions",
      price: 89.99,
      originalPrice: 115.99,
      image: "https://images.pexels.com/photos/4040652/pexels-photo-4040652.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      badge: "Limited Edition"
    },
    {
      id: 2,
      name: "Artisan Tasting Set",
      description: "Explore our signature flavors with this expertly crafted tasting collection",
      price: 64.99,
      image: "https://images.pexels.com/photos/6802983/pexels-photo-6802983.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop",
      badge: "Bestseller"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-amber-900 to-amber-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <Gift className="h-8 w-8 text-amber-300 mr-2" />
            <span className="text-amber-300 font-semibold uppercase tracking-wide">Featured Collections</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-serif mb-6">
            Curated for <span className="text-amber-300">Connoisseurs</span>
          </h2>
          <p className="text-xl text-amber-100 max-w-2xl mx-auto">
            Discover our specially crafted collections, perfect for gifting or treating yourself to the ultimate chocolate experience.
          </p>
        </div>

        {/* Collections Grid */}
        <div className="grid lg:grid-cols-2 gap-8">
          {collections.map((collection, index) => (
            <div
              key={collection.id}
              className={`relative overflow-hidden rounded-2xl group ${
                index === 0 ? 'lg:row-span-2' : ''
              }`}
            >
              {/* Background Image */}
              <div className="absolute inset-0">
                <img
                  src={collection.image}
                  alt={collection.name}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20"></div>
              </div>

              {/* Content */}
              <div className="relative z-10 p-8 h-full flex flex-col justify-end min-h-[400px]">
                {/* Badge */}
                <div className="absolute top-6 left-6">
                  <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold ${
                    collection.badge === 'Limited Edition' 
                      ? 'bg-red-600 text-white'
                      : 'bg-amber-600 text-white'
                  }`}>
                    {collection.badge}
                  </span>
                </div>

                <div className="space-y-4">
                  <h3 className="text-2xl md:text-3xl font-serif font-bold">
                    {collection.name}
                  </h3>
                  <p className="text-white/90 text-lg leading-relaxed">
                    {collection.description}
                  </p>
                  
                  <div className="flex items-center space-x-4">
                    <span className="text-3xl font-bold text-amber-300">
                      ${collection.price}
                    </span>
                    {collection.originalPrice && (
                      <span className="text-lg text-white/60 line-through">
                        ${collection.originalPrice}
                      </span>
                    )}
                  </div>

                  <button className="inline-flex items-center space-x-2 bg-white text-amber-900 px-6 py-3 rounded-full font-semibold hover:bg-amber-50 transition-all duration-300 transform hover:scale-105 group-hover:shadow-lg">
                    <span>Shop Collection</span>
                    <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-serif mb-4">Can't Decide?</h3>
            <p className="text-amber-100 mb-6">
              Let our chocolate experts create a personalized selection just for you.
            </p>
            <button className="bg-amber-600 text-white px-8 py-3 rounded-full font-semibold hover:bg-amber-700 transition-colors duration-300">
              Get Personal Recommendations
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedCollection;